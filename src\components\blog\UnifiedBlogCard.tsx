'use client';

import { motion } from 'framer-motion';
import { Badge } from '@/components/ui/badge';
import { Calendar, User, ArrowRight } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { useState } from 'react';

interface BlogPost {
  id?: string;
  _id?: string;
  title: string;
  excerpt?: string;
  description?: string;
  content?: string;
  slug: string;
  featuredImage?: string;
  image?: string;
  imageCredit?: string;
  categoryId?: string;
  category?: string;
  categories?: string[];
  tags?: string[];
  publishedAt?: string;
  createdAt?: string;
  date?: string;
  author: {
    name: string;
    email?: string;
  } | string;
}

interface UnifiedBlogCardProps {
  post: BlogPost;
  index?: number;
  showAnimation?: boolean;
  className?: string;
}

export function UnifiedBlogCard({
  post,
  index = 0,
  showAnimation = true,
  className = ""
}: UnifiedBlogCardProps) {
  const [imageError, setImageError] = useState(false);
  const [imageLoading, setImageLoading] = useState(true);

  // Normalize post data
  const normalizedPost = {
    id: post.id || post._id || '',
    title: post.title,
    excerpt: post.excerpt || post.description || '',
    slug: post.slug,
    featuredImage: post.featuredImage || post.image || '',
    imageCredit: post.imageCredit || '',
    category: post.category || (post.categories && post.categories[0]) || 'General',
    publishedAt: post.publishedAt || post.createdAt || post.date || new Date().toISOString(),
    author: typeof post.author === 'string' ? post.author : post.author?.name || 'Unknown Author'
  };

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch {
      return 'Recent';
    }
  };

  // Truncate text
  const truncateText = (text: string, maxLength: number) => {
    if (!text) return '';
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  // Get category color with enhanced styling
  const getCategoryColor = (category: string) => {
    const colors = {
      'Technology': 'bg-blue-500/10 text-blue-600 border-blue-500/20 dark:bg-blue-500/20 dark:text-blue-400 dark:border-blue-500/30',
      'Food': 'bg-green-500/10 text-green-600 border-green-500/20 dark:bg-green-500/20 dark:text-green-400 dark:border-green-500/30',
      'Automotive': 'bg-purple-500/10 text-purple-600 border-purple-500/20 dark:bg-purple-500/20 dark:text-purple-400 dark:border-purple-500/30',
      'Design': 'bg-pink-500/10 text-pink-600 border-pink-500/20 dark:bg-pink-500/20 dark:text-pink-400 dark:border-pink-500/30',
      'AI': 'bg-indigo-500/10 text-indigo-600 border-indigo-500/20 dark:bg-indigo-500/20 dark:text-indigo-400 dark:border-indigo-500/30',
      'Web': 'bg-cyan-500/10 text-cyan-600 border-cyan-500/20 dark:bg-cyan-500/20 dark:text-cyan-400 dark:border-cyan-500/30',
      'General': 'bg-gray-500/10 text-gray-600 border-gray-500/20 dark:bg-gray-500/20 dark:text-gray-400 dark:border-gray-500/30'
    };
    return colors[category as keyof typeof colors] || colors.General;
  };

  // Get proper image source with fallbacks
  const getImageSource = () => {
    if (imageError) {
      return 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=800&q=80';
    }

    if (normalizedPost.featuredImage && normalizedPost.featuredImage.trim() !== '') {
      return normalizedPost.featuredImage;
    }

    return '/images/blog-placeholder.jpg';
  };

  const cardVariants = showAnimation ? {
    hidden: { opacity: 0, y: 20, scale: 0.95 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.6,
        delay: index * 0.1,
        ease: "easeOut"
      }
    }
  } : {};

  const hoverVariants = showAnimation ? {
    y: -8,
    scale: 1.02,
    transition: {
      duration: 0.3,
      ease: "easeOut"
    }
  } : {};

  return (
    <Link href={`/blog/${normalizedPost.slug}`} className="block h-full group cursor-pointer">
      <motion.div
        variants={cardVariants}
        initial={showAnimation ? "hidden" : false}
        animate={showAnimation ? "visible" : false}
        whileHover={showAnimation ? hoverVariants : undefined}
        className={`h-full ${className}`}
      >
        <div className="h-full bg-card border border-border rounded-2xl overflow-hidden shadow-sm hover:shadow-xl transition-all duration-500 hover:border-primary/20">
          {/* Cover Image Section */}
          <div className="relative h-52 overflow-hidden bg-muted">
            {/* Loading State */}
            {imageLoading && (
              <div className="absolute inset-0 bg-muted animate-pulse flex items-center justify-center">
                <div className="w-8 h-8 border-2 border-primary/30 border-t-primary rounded-full animate-spin" />
              </div>
            )}

            <Image
              src={getImageSource()}
              alt={normalizedPost.title}
              fill
              className={`object-cover transition-all duration-700 group-hover:scale-110 ${
                imageLoading ? 'opacity-0' : 'opacity-100'
              }`}
              onLoad={() => setImageLoading(false)}
              onError={() => {
                setImageError(true);
                setImageLoading(false);
              }}
              priority={index < 3} // Prioritize first 3 images
            />

            {/* Hover Overlay */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

            {/* Category Badge */}
            <div className="absolute top-4 left-4 z-10">
              <Badge className={`${getCategoryColor(normalizedPost.category)} text-xs font-semibold border backdrop-blur-sm`}>
                {normalizedPost.category}
              </Badge>
            </div>

            {/* Image Credit */}
            {normalizedPost.imageCredit && (
              <div className="absolute bottom-3 right-3 z-10">
                <span className="text-xs text-white/90 bg-black/70 px-2 py-1 rounded-md backdrop-blur-sm">
                  📸 {normalizedPost.imageCredit}
                </span>
              </div>
            )}
          </div>

          {/* Content Section */}
          <div className="p-6 flex flex-col flex-grow">
            {/* Publication Date */}
            <div className="flex items-center gap-2 text-xs text-muted-foreground mb-3">
              <Calendar className="h-3 w-3" />
              <span className="font-medium">{formatDate(normalizedPost.publishedAt)}</span>
            </div>

            {/* Blog Title */}
            <h3 className="text-xl font-bold mb-3 line-clamp-2 text-foreground group-hover:text-primary transition-colors duration-300 leading-tight">
              {normalizedPost.title}
            </h3>

            {/* Short Description/Excerpt */}
            {normalizedPost.excerpt && (
              <p className="text-muted-foreground text-sm mb-4 line-clamp-3 flex-grow leading-relaxed">
                {truncateText(normalizedPost.excerpt, 120)}
              </p>
            )}

            {/* Author Name */}
            <div className="flex items-center justify-between mt-auto pt-4 border-t border-border/50">
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                  <User className="h-4 w-4 text-primary" />
                </div>
                <div>
                  <p className="text-sm font-medium text-foreground">{normalizedPost.author}</p>
                </div>
              </div>

              {/* Read More Arrow */}
              <div className="flex items-center gap-2 text-primary group-hover:gap-3 transition-all duration-300">
                <span className="text-sm font-semibold">Read more</span>
                <motion.div
                  whileHover={{ x: 4 }}
                  transition={{ duration: 0.2 }}
                >
                  <ArrowRight className="h-4 w-4" />
                </motion.div>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </Link>
  );
}
