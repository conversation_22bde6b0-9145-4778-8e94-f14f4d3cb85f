import { NextRequest, NextResponse } from "next/server";
import connectToDatabase from "@/lib/db";
import { User, Category, BlogPost } from "@/models";
import { z } from "zod";

// Schema validation
const BlogPostSchema = z.object({
  title: z.string().min(5, "Title must be at least 5 characters"),
  content: z.string().min(50, "Content must be at least 50 characters"),
  slug: z.string().optional(),
  description: z.string().optional(),
  tags: z.array(z.string()).optional().default([]),
  categories: z.array(z.string()).optional().default([]),
  status: z.enum(["draft", "scheduled", "published", "archived"]).default("draft"),
  visibility: z.enum(["public", "private", "draft"]).optional().default("public"),
  scheduledAt: z.union([z.date(), z.string(), z.null()]).optional().nullable(),
  featuredImage: z.string().optional().default(""),
  imageCredit: z.string().optional().default(""),
});

export async function GET(request: NextRequest) {
  try {
    await connectToDatabase();

    const { searchParams } = new URL(request.url);
    const parsedParams = {
      page: parseInt(searchParams.get("page") || "1"),
      limit: parseInt(searchParams.get("limit") || "10"),
      status: searchParams.get("status"),
      search: searchParams.get("search"),
      category: searchParams.get("category"),
    };

    const query: any = {};

    // Filter by status
    if (parsedParams.status) {
      query.status = parsedParams.status;
    }

    // Filter by category
    if (parsedParams.category) {
      query.categoryId = parsedParams.category;
    }

    // Search functionality
    if (parsedParams.search) {
      query.$or = [
        { title: { $regex: parsedParams.search, $options: "i" } },
        { content: { $regex: parsedParams.search, $options: "i" } },
        { description: { $regex: parsedParams.search, $options: "i" } },
      ];
    }

    console.log("Blog query:", JSON.stringify(query, null, 2));

    const [posts, total] = await Promise.all([
      BlogPost.find(query)
        .sort({ publishedAt: -1, createdAt: -1 })
        .skip((parsedParams.page - 1) * parsedParams.limit)
        .limit(parsedParams.limit)
        .populate("authorId", "name email")
        .populate("categoryId", "name slug")
        .lean(),
      BlogPost.countDocuments(query),
    ]);

    console.log(`Found ${posts.length} posts out of ${total} total`);

    // Format posts to include author and category info safely
    const formattedPosts = posts.map(post => ({
      ...post,
      id: post._id.toString(),
      _id: post._id.toString(),
      author: {
        name: post.authorId?.name || "Admin User",
        email: post.authorId?.email || "<EMAIL>"
      },
      category: post.categoryId?.name || "General",
      categoryId: post.categoryId?._id || post.categoryId || null,
      excerpt: post.description || post.content?.substring(0, 150) + "..." || "",
      featuredImage: post.featuredImage || "/images/blog-placeholder.jpg",
      image: post.featuredImage || "/images/blog-placeholder.jpg", // Keep both for compatibility
      publishedAt: post.publishedAt || post.createdAt,
    }));

    return NextResponse.json({
      success: true,
      data: formattedPosts,
      pagination: {
        total,
        page: parsedParams.page,
        limit: parsedParams.limit,
        totalPages: Math.ceil(total / parsedParams.limit),
      },
    });
  } catch (error) {
    console.error("GET /api/blog error:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch posts", data: [] },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectToDatabase();

    // Get user ID from request headers (set by middleware)
    const userId = request.headers.get("x-user-id");
    if (!userId) {
      console.log("No user ID in headers, creating with default user");
      // For development, use a default user ID if not authenticated
      // In production, you should return unauthorizedResponse();
    }

    const body = await request.json();
    console.log("Received blog post data:", JSON.stringify(body, null, 2));

    const validation = BlogPostSchema.safeParse(body);
    if (!validation.success) {
      console.log("Validation failed:", validation.error);
      return NextResponse.json(
        { error: "Validation failed", details: validation.error.issues },
        { status: 400 }
      );
    }

    // Auto-generate slug if not provided
    const finalSlug = validation.data.slug ||
      validation.data.title.toLowerCase()
        .replace(/[^\w\s]/gi, "")
        .replace(/\s+/g, "-")
        .substring(0, 50);

    // Check if slug already exists
    const existingSlug = await BlogPost.findOne({ slug: finalSlug });
    if (existingSlug) {
      return NextResponse.json(
        { error: "Slug already exists" },
        { status: 409 }
      );
    }

    // Use provided userId or create a default one for development
    const finalUserId = userId || "507f1f77bcf86cd799439011"; // Default ObjectId for development

    const newPost = await BlogPost.create({
      ...validation.data,
      slug: finalSlug,
      authorId: finalUserId,
      scheduledAt: validation.data.status === "scheduled"
        ? validation.data.scheduledAt
        : null,
    });

    return NextResponse.json(newPost, { status: 201 });
  } catch (error) {
    console.error("POST /api/blog error:", error);
    return serverErrorResponse("Failed to create post");
  }
}

// Note: PUT and DELETE are handled by /api/blog/[id]/route.ts

// Helper functions
function unauthorizedResponse() {
  return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
}

function notFoundResponse(message: string) {
  return NextResponse.json({ error: message }, { status: 404 });
}

function invalidRequestResponse(message: string) {
  return NextResponse.json({ error: message }, { status: 400 });
}

function serverErrorResponse(message: string) {
  return NextResponse.json({ error: message }, { status: 500 });
}
